using GCP.Common;
using GCP.Iot.Interfaces;
using GCP.Iot.Models;
using IoTClient;
using IoTClient.Clients.PLC;
using IoTClient.Enums;
using IoTClient.Models;
using Serilog;
using DataTypeEnum = GCP.Iot.Models.DataTypeEnum;
using IotDataTypeEnum = IoTClient.Enums.DataTypeEnum;

namespace GCP.Iot.OmronFins
{
    [DriverInfo("提供与欧姆龙PLC的连接。支持FINS协议，适用于CP、CJ、CS系列PLC。")]
    public class OmronFinsDriver : IDriver
    {
        private OmronFinsClient _client;

        public OmronFinsDriver()
        {
        }

        public bool IsConnected => _client is { Connected: true };

        private ILogger _logger;
        public ILogger Logger
        {
            get => _logger;
            set
            {
                _logger = value;
                _logger?.Information($"OmronFins驱动初始化完成");
            }
        }

        public string DriverCode => "OmronFins";
        public bool SupportsBatchReading => true;

        #region 配置参数

        [DriverParameter("最小采集周期(毫秒)")]
        public int MinSamplingPeriod { get; set; } = 1000;

        [DriverParameter("IP地址")]
        public string IpAddress { get; set; } = "*************";

        [DriverParameter("端口号")]
        public int Port { get; set; } = 9600;

        [DriverParameter("超时时间ms")]
        public int Timeout { get; set; } = 3000;

        [DriverParameter("存档周期(毫秒)")]
        public int? ArchivePeriod { get; set; }

        #endregion

        public async Task<bool> ConnectAsync()
        {
            try
            {
                _client?.Close();

                _client = new OmronFinsClient(IpAddress, Port, timeout: Timeout);

                var result = _client.Open();
                if (!result.IsSucceed)
                {
                    throw result.Exception;
                }

                Logger?.Information($"OmronFins驱动连接成功: {IpAddress}:{Port}");
                return true;
            }
            catch (Exception ex)
            {
                Logger?.Error(ex, $"OmronFins驱动连接失败: {IpAddress}:{Port}");
                return false;
            }
        }

        public async Task<bool> DisconnectAsync()
        {
            try
            {
                _client?.Close();
                Logger?.Information("OmronFins驱动断开连接");
                return true;
            }
            catch (Exception ex)
            {
                Logger?.Error(ex, "OmronFins驱动断开连接失败");
                return false;
            }
        }

        [DriverMethod("读OmronFins", description: "读OmronFins地址")]
        public Task<DriverOperationResult> ReadAsync(string address, DataTypeEnum dataType)
        {
            var result = new DriverOperationResult
            {
                Status = OperationStatus.Success,
                VariableId = address,
            };

            try
            {
                if (!IsConnected)
                {
                    throw new InvalidOperationException("设备未连接");
                }

                var iotDataType = ConvertToIotDataType(dataType);
                var readResult = _client.Read(address, iotDataType);

                if (!readResult.IsSucceed)
                {
                    throw readResult.Exception;
                }

                result.Value = readResult.Value;
                result.Timestamp = DateTime.Now;
            }
            catch (Exception ex)
            {
                result.Status = OperationStatus.Failed;
                result.ErrorMessage = ex.Message;
                Logger?.Error(ex, $"读取OmronFins地址失败: {address}");
            }

            return Task.FromResult(result);
        }

        public Task<DriverOperationResult> BatchReadAsync(Dictionary<string, DataTypeEnum> addresses)
        {
            var result = new DriverOperationResult
            {
                Status = OperationStatus.Success,
                Timestamp = DateTime.Now
            };

            try
            {
                if (!IsConnected)
                {
                    throw new InvalidOperationException("设备未连接");
                }

                var iotAddresses = addresses.ToDictionary(
                    kvp => kvp.Key,
                    kvp => ConvertToIotDataType(kvp.Value)
                );

                var readResult = _client.BatchRead(iotAddresses);

                if (!readResult.IsSucceed)
                {
                    throw readResult.Exception;
                }

                result.Values = readResult.Value;
            }
            catch (Exception ex)
            {
                result.Status = OperationStatus.Failed;
                result.ErrorMessage = ex.Message;
                Logger?.Error(ex, "批量读取OmronFins地址失败");
            }

            return Task.FromResult(result);
        }

        [DriverMethod("写OmronFins", description: "写OmronFins地址")]
        public Task<DriverOperationResult> WriteAsync(string address, object value, DataTypeEnum dataType)
        {
            var result = new DriverOperationResult
            {
                Status = OperationStatus.Success,
                VariableId = address,
                Timestamp = DateTime.Now
            };

            try
            {
                if (!IsConnected)
                {
                    throw new InvalidOperationException("设备未连接");
                }

                var iotDataType = ConvertToIotDataType(dataType);
                var writeResult = _client.Write(address, value, iotDataType);

                if (!writeResult.IsSucceed)
                {
                    throw writeResult.Exception;
                }

                result.Value = value;
            }
            catch (Exception ex)
            {
                result.Status = OperationStatus.Failed;
                result.ErrorMessage = ex.Message;
                Logger?.Error(ex, $"写入OmronFins地址失败: {address}");
            }

            return Task.FromResult(result);
        }

        private IotDataTypeEnum ConvertToIotDataType(DataTypeEnum dataType)
        {
            return dataType switch
            {
                DataTypeEnum.Bool => IotDataTypeEnum.Bool,
                DataTypeEnum.Byte => IotDataTypeEnum.Byte,
                DataTypeEnum.SByte => IotDataTypeEnum.SByte,
                DataTypeEnum.UShort => IotDataTypeEnum.UShort,
                DataTypeEnum.Short => IotDataTypeEnum.Short,
                DataTypeEnum.UInt => IotDataTypeEnum.UInt,
                DataTypeEnum.Int => IotDataTypeEnum.Int,
                DataTypeEnum.ULong => IotDataTypeEnum.ULong,
                DataTypeEnum.Long => IotDataTypeEnum.Long,
                DataTypeEnum.Float => IotDataTypeEnum.Float,
                DataTypeEnum.Double => IotDataTypeEnum.Double,
                DataTypeEnum.String => IotDataTypeEnum.String,
                _ => throw new ArgumentException($"不支持的数据类型: {dataType}")
            };
        }

        public void Dispose()
        {
            _client?.Close();
            _client?.Dispose();
        }
    }
}
